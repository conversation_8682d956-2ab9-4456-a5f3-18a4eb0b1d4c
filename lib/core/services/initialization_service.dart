import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'token_service/token_storage.dart';

enum AppInitialRoute {
  onboarding,
  login,
  home,
}

class InitializationService {
  final TokenStorage _tokenStorage;
  final FlutterSecureStorage _secureStorage;

  InitializationService(this._tokenStorage, this._secureStorage);

  Future<AppInitialRoute> determineInitialRoute() async {
    try {
      // Check if this is the first launch
      final isFirstLaunch = await _checkFirstLaunch();
      
      if (isFirstLaunch) {
        // Mark that the app has been launched before
        await _markAppAsLaunched();
        return AppInitialRoute.onboarding;
      }

      // Check if user has a valid token
      final token = await _tokenStorage.getToken();
      final userId = await _tokenStorage.getUserId();
      
      if (token.isNotEmpty && userId > 0) {
        // User is logged in, go to home
        return AppInitialRoute.home;
      } else {
        // User is not logged in, go to login
        return AppInitialRoute.login;
      }
    } catch (e) {
      // In case of any error, default to onboarding
      return AppInitialRoute.onboarding;
    }
  }

  Future<bool> _checkFirstLaunch() async {
    const key = 'app_launched_before';
    final value = await _secureStorage.read(key: key);
    return value == null || value.isEmpty;
  }

  Future<void> _markAppAsLaunched() async {
    const key = 'app_launched_before';
    await _secureStorage.write(key: key, value: 'true');
  }

  Future<void> clearUserData() async {
    // Clear token and user data (for logout)
    await _tokenStorage.clearAll();
  }
}
