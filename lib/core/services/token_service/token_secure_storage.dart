import 'package:edus_student_ms/core/services/token_service/token_storage.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class TokenFlutterSecureStorageService implements TokenStorage {
  final FlutterSecureStorage _preferences;

  const TokenFlutterSecureStorageService(this._preferences);

  @override
  Future<String> getToken() async {
    return await _preferences.read(key: 'token') ?? '';
  }

  @override
  void storeToken(String token) async {
    _preferences.write(key: 'token', value: token);
  }

  @override
  Future<int> getUserId() async {
    // TODO: implement getUserId
    var val = await _preferences.read(key: 'userId') ?? "0";
    return int.tryParse(val) ?? 0;
  }

  @override
  void storeUserId(int id) {
    _preferences.write(key: 'userId', value: id.toString());
  }

  @override
  Future<void> clearAll() async {
    await _preferences.delete(key: 'token');
    await _preferences.delete(key: 'userId');
  }
}
