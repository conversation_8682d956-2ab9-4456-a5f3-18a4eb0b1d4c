import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/api_constant.dart';
import '../../../../core/services/service_locator.dart';
import '../../../../core/services/token_service/token_storage.dart';
import '../models/attendance_data_model.dart';

abstract class AttendanceRemoteDataSource {
  Future<AttendanceDataModel> getAttendanceRecords({
    required String language,
    required String date,
  });
}

final dio = Dio();

class AttendanceRemoteDataSourceImpl implements AttendanceRemoteDataSource {
  AttendanceRemoteDataSourceImpl();

  @override
  Future<AttendanceDataModel> getAttendanceRecords({
    required String language,
    required String date,
  }) async {
    final token = await sl<TokenStorage>().getToken();
    final response = await dio.get(
      ApiConstants.attendanceRecordsUrl,
      queryParameters: {
        'language': language,
        'date': date,
      },
      options: Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
      ),
    );

    log("Attendance Records API Response: ${response.data}");

    return switch (response.statusCode!) {
      >= 200 && < 300 =>
        AttendanceDataModel.fromJson(const JsonDecoder().convert(response.data)),
      404 => throw ServerException<String>("Attendance records not found"),
      >= 400 && < 500 => throw ServerException<String>(response.data),
      _ => throw ServerException("${response.data}")
    };
  }
}
