import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/api_constant.dart';
import '../../../../core/services/service_locator.dart';
import '../../../../core/services/token_service/token_storage.dart';
import '../models/qr_data_model.dart';

abstract class QrRemoteDataSource {
  Future<QrDataModel> getQrData({
    required String language,
  });
}

final dio = Dio();

class QrRemoteDataSourceImpl implements QrRemoteDataSource {
  QrRemoteDataSourceImpl();

  @override
  Future<QrDataModel> getQrData({
    required String language,
  }) async {
    final token = await sl<TokenStorage>().getToken();
    final response = await dio.get(
      ApiConstants.qrCodeUrl,
      queryParameters: {
        'language': language,
      },
      options: Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
      ),
    );

    log("QR Code API Response: ${response.data}");

    return switch (response.statusCode!) {
      >= 200 && < 300 =>
        QrDataModel.fromJson(const JsonDecoder().convert(response.data)),
      404 => throw ServerException<String>("QR data not found"),
      >= 400 && < 500 => throw ServerException<String>(response.data),
      _ => throw ServerException("${response.data}")
    };
  }
}
