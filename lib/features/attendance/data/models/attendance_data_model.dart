import '../../domain/entities/attendance_data.dart';

class AttendanceDataModel extends AttendanceData {
  const AttendanceDataModel({
    required super.status,
    required super.records,
  });

  factory AttendanceDataModel.fromJson(Map<String, dynamic> json) {
    return AttendanceDataModel(
      status: json['status'] ?? '',
      records: json['data'] != null
          ? AttendanceRecordsModel.fromJson(json['data'])
          : const AttendanceRecordsModel(attendedClasses: 0, missedClasses: 0),
    );
  }
}

class AttendanceRecordsModel extends AttendanceRecords {
  const AttendanceRecordsModel({
    required super.attendedClasses,
    required super.missedClasses,
  });

  factory AttendanceRecordsModel.fromJson(Map<String, dynamic> json) {
    return AttendanceRecordsModel(
      attendedClasses: json['AttendedClasses'] ?? 0,
      missedClasses: json['MissedClasses'] ?? 0,
    );
  }
}
