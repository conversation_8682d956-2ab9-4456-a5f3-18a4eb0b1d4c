import '../../domain/entities/qr_data.dart';

class QrDataModel extends QrData {
  const QrDataModel({
    required super.status,
    required super.students,
  });

  factory QrDataModel.fromJson(Map<String, dynamic> json) {
    return QrDataModel(
      status: json['status'] ?? '',
      students: json['data'] != null
          ? (json['data'] as List)
              .map((x) => StudentInfoModel.fromJson(x))
              .toList()
          : [],
    );
  }
}

class StudentInfoModel extends StudentInfo {
  const StudentInfoModel({
    required super.studentName,
    required super.email,
    required super.contactNumber,
    required super.address,
  });

  factory StudentInfoModel.fromJson(Map<String, dynamic> json) {
    return StudentInfoModel(
      studentName: json['student_name'] ?? '',
      email: json['email'] ?? '',
      contactNumber: json['contact_number'] ?? '',
      address: json['address'] ?? '',
    );
  }
}
