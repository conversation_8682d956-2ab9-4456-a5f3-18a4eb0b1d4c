import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/qr_data.dart';
import '../../domain/repository/qr_repository.dart';
import '../datasource/qr_remote_datasource.dart';

class QrRepositoryImpl implements QrRepository {
  final QrRemoteDataSource remoteDataSource;

  QrRepositoryImpl(this.remoteDataSource);

  @override
  Future<Either<Failure, QrData>> getQrData({
    required String language,
  }) async {
    try {
      final result = await remoteDataSource.getQrData(
        language: language,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
}
