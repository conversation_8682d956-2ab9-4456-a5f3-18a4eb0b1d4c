import 'package:equatable/equatable.dart';

class AttendanceData extends Equatable {
  final String status;
  final AttendanceRecords records;

  const AttendanceData({
    required this.status,
    required this.records,
  });

  @override
  List<Object?> get props => [status, records];
}

class AttendanceRecords extends Equatable {
  final int attendedClasses;
  final int missedClasses;

  const AttendanceRecords({
    required this.attendedClasses,
    required this.missedClasses,
  });

  @override
  List<Object?> get props => [attendedClasses, missedClasses];
}
