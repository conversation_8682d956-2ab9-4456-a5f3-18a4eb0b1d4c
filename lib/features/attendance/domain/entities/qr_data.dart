import 'package:equatable/equatable.dart';

class QrData extends Equatable {
  final String status;
  final List<StudentInfo> students;

  const QrData({
    required this.status,
    required this.students,
  });

  @override
  List<Object?> get props => [status, students];
}

class StudentInfo extends Equatable {
  final String studentName;
  final String email;
  final String contactNumber;
  final String address;

  const StudentInfo({
    required this.studentName,
    required this.email,
    required this.contactNumber,
    required this.address,
  });

  @override
  List<Object?> get props => [studentName, email, contactNumber, address];
}
