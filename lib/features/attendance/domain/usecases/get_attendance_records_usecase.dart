import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecase/base_usecase.dart';
import '../entities/attendance_data.dart';
import '../repository/attendance_repository.dart';

class GetAttendanceRecordsUseCase implements UseCase<AttendanceData, AttendanceParams> {
  final AttendanceRepository repository;

  GetAttendanceRecordsUseCase(this.repository);

  @override
  Future<Either<Failure, AttendanceData>> call(AttendanceParams params) async {
    return await repository.getAttendanceRecords(
      language: params.language,
      date: params.date,
    );
  }
}

class AttendanceParams extends Equatable {
  final String language;
  final String date;

  const AttendanceParams({
    required this.language,
    required this.date,
  });

  @override
  List<Object?> get props => [language, date];
}
