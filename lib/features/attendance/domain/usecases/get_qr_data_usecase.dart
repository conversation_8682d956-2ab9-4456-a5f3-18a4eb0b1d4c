import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecase/base_usecase.dart';
import '../entities/qr_data.dart';
import '../repository/qr_repository.dart';

class GetQrDataUseCase implements UseCase<QrData, QrParams> {
  final QrRepository repository;

  GetQrDataUseCase(this.repository);

  @override
  Future<Either<Failure, QrData>> call(QrParams params) async {
    return await repository.getQrData(
      language: params.language,
    );
  }
}

class QrParams extends Equatable {
  final String language;

  const QrParams({
    required this.language,
  });

  @override
  List<Object?> get props => [language];
}
