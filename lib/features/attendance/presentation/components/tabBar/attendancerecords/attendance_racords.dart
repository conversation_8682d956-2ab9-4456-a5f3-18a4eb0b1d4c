import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/enums.dart';
import 'package:edus_student_ms/features/attendance/presentation/components/tabBar/attendancerecords/attended_number.dart';
import 'package:edus_student_ms/features/attendance/presentation/components/tabBar/attendancerecords/calender_attendace.dart';
import 'package:edus_student_ms/features/attendance/presentation/components/tabBar/attendancerecords/missed_number.dart';
import 'package:edus_student_ms/features/classes/presentation/components/calender.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../../controller/attendance_bloc.dart';

class AttendaceRecords extends StatefulWidget {
  const AttendaceRecords({super.key});

  @override
  State<AttendaceRecords> createState() => _AttendaceRecordsState();
}

class _AttendaceRecordsState extends State<AttendaceRecords> {
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    // Load attendance records for today initially
    _loadAttendanceForDate(_selectedDate);
  }

  void _loadAttendanceForDate(DateTime date) {
    final dateString = DateFormat('yyyy-MM-dd').format(date);

    print('Loading attendance for date: $dateString');

    context.read<AttendanceBloc>().add(GetAttendanceRecordsEvent(
      language: 'en',
      date: dateString,
    ));
  }

  void _onDateSelected(DateTime selectedDate) {
    print('Attendance: Date selected: $selectedDate');
    setState(() {
      _selectedDate = selectedDate;
    });
    _loadAttendanceForDate(selectedDate);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AttendanceBloc, AttendanceState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(
              height: 24.h,
            ),
            CalendarWidgetAttendance(
              selectedDate: _selectedDate,
              onDateSelected: _onDateSelected,
            ),
            SizedBox(
              height: 16.h,
            ),
            if (state.status == RequestStatus.loading)
              const Center(child: CircularProgressIndicator(color: AppColor.kPrimaryColor,))
            else if (state.status == RequestStatus.error)
              Center(
                child: Column(
                  children: [
                    PoppinsText(
                      text: "Error loading attendance",
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.red,
                    ),
                    SizedBox(height: 16.h),
                    ElevatedButton(
                      onPressed: () => _loadAttendanceForDate(_selectedDate),
                      child: const PoppinsText(
                        text: "Retry",
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              )
            else
              Padding(
                padding: EdgeInsets.symmetric(vertical: 4.h),
                child: Column(
                  children: [
                    AttendedNumber(
                      attendedCount: state.attendanceData?.records.attendedClasses ?? 0,
                    ),
                    SizedBox(height: 8.h),
                    MissedNumber(
                      missedCount: state.attendanceData?.records.missedClasses ?? 0,
                    )
                  ],
                ),
              )
          ],
        );
      },
    );
  }
}
