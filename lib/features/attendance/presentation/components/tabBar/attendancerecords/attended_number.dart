import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AttendedNumber extends StatelessWidget {
  final int attendedCount;

  const AttendedNumber({super.key, this.attendedCount = 0});

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان حسب العدد
    final bool hasAttendance = attendedCount > 0;
    final Color backgroundColor = hasAttendance
        ? const Color(0xFFE8F5E8) // أخضر فاتح عند وجود حضور
        : const Color(0xFFF5F5F5); // رمادي فاتح عند عدم وجود حضور

    final Color iconColor = hasAttendance
        ? const Color(0xFF4CAF50) // أخضر غامق عند وجود حضور
        : const Color(0xFFBDBDBD); // رمادي عند عدم وجود حضور

    final Color numberColor = hasAttendance
        ? const Color(0xFF2E7D32) // أخضر داكن عند وجود حضور
        : const Color(0xFF9E9E9E); // رمادي عند عدم وجود حضور

    final Color textColor = hasAttendance
        ? const Color(0xFF4CAF50) // أخضر عند وجود حضور
        : const Color(0xFFBDBDBD); // رمادي عند عدم وجود حضور

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: backgroundColor,
        boxShadow: hasAttendance ? [
          BoxShadow(
            color: Colors.green.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : [],
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أيقونة الحضور
          Container(
            width: 50.w,
            height: 50.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25.r),
              color: iconColor,
              boxShadow: hasAttendance ? [
                BoxShadow(
                  color: Colors.green.withOpacity(0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ] : [],
            ),
            child: Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 28,
            ),
          ),
          SizedBox(width: 16.w),
          // النصوص
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                attendedCount.toString(),
                style: AppFont.kFontPoppinsStyle.copyWith(
                  fontSize: 32.sp,
                  fontWeight: FontWeight.w700,
                  color: numberColor,
                ),
              ),
              Text(
                'Attended',
                style: AppFont.kFontPoppinsStyle.copyWith(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: textColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}