import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AttendedNumber extends StatelessWidget {
  final int attendedCount;

  const AttendedNumber({super.key, this.attendedCount = 0});

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان حسب العدد
    final bool hasAttendance = attendedCount > 0;
    final Color backgroundColor = hasAttendance
        ? const Color.fromRGBO(204, 242, 213, 1) // أخضر فاتح عند وجود حضور
        : const Color(0xFFF5F5F5); // رمادي فاتح عند عدم وجود حضور

    final Color sideBarColor = hasAttendance
        ? const Color.fromRGBO(52, 199, 89, 1) // أخضر غامق عند وجود حضور
        : const Color(0xFFE0E0E0); // رمادي عند عدم وجود حضور

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
        color: backgroundColor,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      child: Row(
        children: [
          Container(
            width: 45.w,
            height: 60.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(5.r),
                  bottomLeft: Radius.circular(5.r)),
              color: sideBarColor,
            ),
          ),
          SizedBox(width: 8.w),
          Container(
            decoration: const BoxDecoration(),
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  attendedCount.toString(),
                  textAlign: TextAlign.center,
                  style: AppFont.kFontPoppinsStyle.copyWith(
                      fontSize: 40.sp, fontWeight: FontWeight.w600),
                ),
                SizedBox(width: 8.w),
                Text(
                  'Attended',
                  textAlign: TextAlign.center,
                  style: AppFont.kFontPoppinsStyle.copyWith(
                      fontSize: 17.sp, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}