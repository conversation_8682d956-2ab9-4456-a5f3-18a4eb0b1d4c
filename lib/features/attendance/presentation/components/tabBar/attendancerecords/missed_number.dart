import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MissedNumber extends StatelessWidget {
  final int missedCount;

  const MissedNumber({super.key, this.missedCount = 0});

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان حسب العدد
    final bool hasMissed = missedCount > 0;
    final Color backgroundColor = hasMissed
        ? const Color(0xFFFFF3F3) // أحمر فاتح عند وجود غياب
        : const Color(0xFFF5F5F5); // رمادي فاتح عند عدم وجود غياب

    final Color iconColor = hasMissed
        ? const Color(0xFFF44336) // أحمر غامق عند وجود غياب
        : const Color(0xFFBDBDBD); // رمادي عند عدم وجود غياب

    final Color numberColor = hasMissed
        ? const Color(0xFFD32F2F) // أحمر داكن عند وجود غياب
        : const Color(0xFF9E9E9E); // رمادي عند عدم وجود غياب

    final Color textColor = hasMissed
        ? const Color(0xFFF44336) // أحمر عند وجود غياب
        : const Color(0xFFBDBDBD); // رمادي عند عدم وجود غياب

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: backgroundColor,
        boxShadow: hasMissed ? [
          BoxShadow(
            color: Colors.red.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : [],
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أيقونة الغياب
          Container(
            width: 50.w,
            height: 50.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25.r),
              color: iconColor,
              boxShadow: hasMissed ? [
                BoxShadow(
                  color: Colors.red.withOpacity(0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ] : [],
            ),
            child: Icon(
              Icons.cancel,
              color: Colors.white,
              size: 28,
            ),
          ),
          SizedBox(width: 16.w),
          // النصوص
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                missedCount.toString().padLeft(2, '0'),
                style: AppFont.kFontPoppinsStyle.copyWith(
                  fontSize: 32.sp,
                  fontWeight: FontWeight.w700,
                  color: numberColor,
                ),
              ),
              Text(
                'Missed',
                style: AppFont.kFontPoppinsStyle.copyWith(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: textColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
