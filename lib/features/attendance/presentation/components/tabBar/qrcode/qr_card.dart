import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/enums.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../controller/qr_bloc.dart';

class QrCardInfo extends StatefulWidget {
  const QrCardInfo({super.key});

  @override
  State<QrCardInfo> createState() => _QrCardInfoState();
}

class _QrCardInfoState extends State<QrCardInfo> {
  @override
  void initState() {
    super.initState();
    // Load QR data when component initializes
    context.read<QrBloc>().add(const GetQrDataEvent(language: 'en'));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<QrBloc, QrState>(
      builder: (context, state) {
        if (state.status == RequestStatus.loading) {
          return Container(
            height: 106.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                border: Border.all(color: AppColor.kGrayColor)),
            child: const Center(
              child: CircularProgressIndicator(color: AppColor.kPrimaryColor,),
            ),
          );
        }

        if (state.status == RequestStatus.error) {
          return Container(
            height: 106.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                border: Border.all(color: AppColor.kGrayColor)),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const PoppinsText(
                    text: "Error loading student info",
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.red,
                  ),
                  SizedBox(height: 8.h),
                  ElevatedButton(
                    onPressed: () {
                      context.read<QrBloc>().add(const GetQrDataEvent(language: 'en'));
                    },
                    child: const PoppinsText(
                      text: "Retry",
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Get student info (assuming first student in the list)
        final studentInfo = state.qrData?.students.isNotEmpty == true
            ? state.qrData!.students.first
            : null;

        return Container(
          height: 106.h,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.r),
              border: Border.all(color: AppColor.kGrayColor)),
          child: Padding(
            padding: EdgeInsets.all(15.h),
            child: Column(children: [
              Row(children: [
                const PoppinsText(
                  text: "Name : ",
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                ),SizedBox(width: 10.w,),
                PoppinsText(
                    text: studentInfo?.studentName ?? "No Name",
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    textOverflow: TextOverflow.ellipsis,
                  ),
                
              ]),
              Row(children: [
                const PoppinsText(
                  text: "Phone Number : ",
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                ),
                SizedBox(width: 10.w,),
                PoppinsText(
                    text: studentInfo?.contactNumber ?? "No Phone",
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    textOverflow: TextOverflow.ellipsis,
                  ),
                
              ]),
              Row(children: [
                const PoppinsText(
                  text: "Address : ",
                  fontSize: 17,
                  fontWeight: FontWeight.w500,
                ),SizedBox(width: 10.w,),
                PoppinsText(
                    text: studentInfo?.address ?? "No Address",
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    textOverflow: TextOverflow.ellipsis,
                  ),
               
              ])
            ]),
          ),
        );
      },
    );
  }
}