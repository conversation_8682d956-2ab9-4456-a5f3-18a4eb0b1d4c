import 'dart:async';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/utils/enums.dart';
import '../../domain/entities/attendance_data.dart';
import '../../domain/usecases/get_attendance_records_usecase.dart';


class AttendanceBloc extends Bloc<AttendanceEvent, AttendanceState> {
  final GetAttendanceRecordsUseCase _getAttendanceRecordsUseCase;

  AttendanceBloc(this._getAttendanceRecordsUseCase) : super(const AttendanceState()) {
    on<GetAttendanceRecordsEvent>(_getAttendanceRecordsEvent);
  }

  Future<void> _getAttendanceRecordsEvent(
      GetAttendanceRecordsEvent event, Emitter<AttendanceState> emit) async {
    emit(state.copyWith(status: RequestStatus.loading));

    final result = await _getAttendanceRecordsUseCase(AttendanceParams(
      language: event.language,
      date: event.date,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: RequestStatus.error,
        error: failure.toString(),
      )),
      (attendanceData) => emit(state.copyWith(
        status: RequestStatus.success,
        attendanceData: attendanceData,
      )),
    );
  }
}

// Events
abstract class AttendanceEvent extends Equatable {
  const AttendanceEvent();

  @override
  List<Object> get props => [];
}

class GetAttendanceRecordsEvent extends AttendanceEvent {
  final String language;
  final String date;

  const GetAttendanceRecordsEvent({
    required this.language,
    required this.date,
  });

  @override
  List<Object> get props => [language, date];
}

// State
class AttendanceState extends Equatable {
  final RequestStatus status;
  final AttendanceData? attendanceData;
  final String error;

  const AttendanceState({
    this.status = RequestStatus.initial,
    this.attendanceData,
    this.error = '',
  });

  AttendanceState copyWith({
    RequestStatus? status,
    AttendanceData? attendanceData,
    String? error,
  }) {
    return AttendanceState(
      status: status ?? this.status,
      attendanceData: attendanceData ?? this.attendanceData,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [status, attendanceData, error];
}
