import 'dart:async';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/utils/enums.dart';
import '../../domain/entities/qr_data.dart';
import '../../domain/usecases/get_qr_data_usecase.dart';


class QrBloc extends Bloc<QrEvent, QrState> {
  final GetQrDataUseCase _getQrDataUseCase;

  QrBloc(this._getQrDataUseCase) : super(const QrState()) {
    on<GetQrDataEvent>(_getQrDataEvent);
  }

  Future<void> _getQrDataEvent(
      GetQrDataEvent event, Emitter<QrState> emit) async {
    emit(state.copyWith(status: RequestStatus.loading));

    final result = await _getQrDataUseCase(QrParams(
      language: event.language,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: RequestStatus.error,
        error: failure.toString(),
      )),
      (qrData) => emit(state.copyWith(
        status: RequestStatus.success,
        qrData: qrData,
      )),
    );
  }
}

// Events
abstract class QrEvent extends Equatable {
  const QrEvent();

  @override
  List<Object> get props => [];
}

class GetQrDataEvent extends QrEvent {
  final String language;

  const GetQrDataEvent({
    required this.language,
  });

  @override
  List<Object> get props => [language];
}

// State
class QrState extends Equatable {
  final RequestStatus status;
  final QrData? qrData;
  final String error;

  const QrState({
    this.status = RequestStatus.initial,
    this.qrData,
    this.error = '',
  });

  QrState copyWith({
    RequestStatus? status,
    QrData? qrData,
    String? error,
  }) {
    return QrState(
      status: status ?? this.status,
      qrData: qrData ?? this.qrData,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [status, qrData, error];
}
