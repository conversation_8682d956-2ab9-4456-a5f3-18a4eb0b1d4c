part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();
}

class DoLoginEvent extends AuthEvent {
  final LoginParams params;

  const DoLoginEvent(
    this.params,
  );

  @override
  List<Object> get props => [
        params,
      ];
}

class DoSignUpEvent extends AuthEvent {
  final SignUpParams params;

  const DoSignUpEvent(
    this.params,
  );

  @override
  List<Object> get props => [params];
}

class ResetPasswordEvent extends AuthEvent {
  final String email;

  const ResetPasswordEvent(this.email);

  @override
  List<Object> get props => [email];
}

class LogoutEvent extends AuthEvent {
  const LogoutEvent();

  @override
  List<Object> get props => [];
}
