import 'package:edus_student_ms/core/services/service_locator.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/core/utils/navigation.dart';
import 'package:edus_student_ms/features/auth/domain/usecases/get_login_data_usecase.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:form_validator/form_validator.dart';

import '../../../../../core/config/app_routes.dart';

import '../../../../../core/utils/app_color.dart';
import '../../../../../core/utils/app_font.dart';
import '../../../../../core/utils/enums.dart';
import '../../../../../core/widgets/buttons/login_button.dart';
import '../../../../../core/widgets/input/password_input.dart';
import '../../../../../core/widgets/input/text_input.dart';
import '../../controller/auth_bloc.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        backgroundColor: AppColor.kBgColor,
        body: Form(
          key: _formKey,
          child: SafeArea(
            child: Column(
              children: [
                SizedBox(
                  height: 18.h,
                ),
                Center(child: SvgPicture.asset(AppIcon.kLogo)),
                SizedBox(
                  height: 40.h,
                ),
                TextInput(
                  icon: AppIcon.kpersonalEmail,
                  hint: AppString.emailAddress,
                  inputType: TextInputType.emailAddress,
                  controller: emailController,
                  validator: ValidationBuilder().email().build(),
                ),
                SizedBox(
                  height: 10.h,
                ),
                PasswordInput(
                  icon: AppIcon.kLockIcon,
                  hint: AppString.password,
                  inputType: TextInputType.visiblePassword,
                  controller: passwordController,
                  validator: ValidationBuilder().minLength(8).build(),
                ),
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                  child: InkWell(
                    onTap: () {},
                    child: Container(
                      alignment: Alignment.centerRight,
                      child: const PoppinsText(
                        text: AppString.forgotPassword,
                        fontSize: 12,
                        color: AppColor.kGrayColor,
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.h,
                ),
                BlocConsumer<AuthBloc, AuthState>(listener: (context, state) {
                  if (ModalRoute.of(context)!.isCurrent) {
                    if (state.status == RequestStatus.success) {
                      print("Current Status: ${state.status}");
                      Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BottomNavigation(),
                          ));
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text("Success"),
                          duration: Duration(seconds: 2),
                        ),
                      );
                      // ScaffoldMessenger.of(context)
                      //     .showSnackBar(
                      //   SnackBar(
                      //     content: Text(state
                      //         .signUpData!.message),
                      //     duration:
                      //         Duration(seconds: 2),
                      //   ),
                      // );
                    } else if (state.status == RequestStatus.error) {
                      print("Current Status: ${state.status}");
                      print("Current Error: ${state.error}");
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(state.error),
                          duration: Duration(seconds: 2),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }, builder: (context, state) {
                  return state.status == RequestStatus.loading
                      ? SizedBox(
                        height: 48.h,
                          child: Center(
                            child: const CircularProgressIndicator(
                              color: AppColor.kPrimaryColor,
                            ),
                          ),
                        )
                      : LogInButton(
                          onPress: () {
                            if (!_formKey.currentState!.validate()) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'Please fill out all fields correctly'),
                                  duration: Duration(seconds: 2),
                                ),
                              );
                            } else {
                              sl<AuthBloc>().add(DoLoginEvent(LoginParams(
                                email: emailController.text,
                                password: passwordController.text,
                                language: "en",
                              )));
                            }
                          },
                          isButtonDisabled: false,
                        );
                }),
                SizedBox(
                  height: 20.h,
                ),
                Row(
                  children: [
                    SizedBox(
                      width: 16.w,
                    ),
                    SizedBox(
                      width: 154.w,
                      child: Divider(
                        thickness: 2,
                        color: const Color(0xffCACACA),
                        height: 2.h,
                      ),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    const PoppinsText(
                      text: AppString.or,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Color(0xff777777),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    SizedBox(
                      width: 154.w,
                      child: Divider(
                        thickness: 1,
                        color: const Color(0xffCACACA),
                        height: 1.h,
                      ),
                    ),
                    SizedBox(
                      width: 16.w,
                    ),
                  ],
                ),
                SizedBox(
                  height: 20.h,
                ),
                Container(
                  height: 48.h,
                  width: 343.w,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: AppColor.kBgColor,
                      border: Border.all(color: const Color(0xffD9D9D9))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        AppIcon.kGoogleIcon,
                        height: 24.h,
                        width: 24.h,
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      const PoppinsText(
                        text: AppString.logInWithGoogle,
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 8.h,
                ),
                Container(
                  height: 48.h,
                  width: 343.w,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: AppColor.kBgColor,
                      border: Border.all(color: const Color(0xffD9D9D9))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        AppIcon.kFacebookIcon,
                        height: 24.h,
                        width: 24.h,
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      const PoppinsText(
                        text: AppString.logInWithFacebook,
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 8.h,
                ),
                Container(
                  height: 48.h,
                  width: 343.w,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      color: AppColor.kBgColor,
                      border: Border.all(color: const Color(0xffD9D9D9))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        AppIcon.kAppleIcon,
                        height: 24.h,
                        width: 24.h,
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      const PoppinsText(
                        text: AppString.logInWithApple,
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ],
                  ),
                ),
              
                const Spacer(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const PoppinsText(
                      text: AppString.newToEdus,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.pushReplacementNamed(context, kSignup);
                      },
                      child: const PoppinsText(
                        text: AppString.bSignUp,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColor.kPrimaryColor,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 20.h,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
