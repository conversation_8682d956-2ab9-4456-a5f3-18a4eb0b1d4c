import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/api_constant.dart';
import '../../../../core/services/service_locator.dart';
import '../../../../core/services/token_service/token_storage.dart';
import '../models/class_data_model.dart';

abstract class ClassesRemoteDataSource {
  Future<ClassDataModel> getClasses({
    required String classDay,
    required String language,
    required String date,
  });
}

final dio = Dio();

class ClassesRemoteDataSourceImpl implements ClassesRemoteDataSource {
  ClassesRemoteDataSourceImpl();

  @override
  Future<ClassDataModel> getClasses({
    required String classDay,
    required String language,
    required String date,
  }) async {
    final token = await sl<TokenStorage>().getToken();
    final response = await dio.get(
      ApiConstants.classesUrl,
      queryParameters: {
        'class_day': classDay,
        'language': language,
        'date': date,
      },
      options: Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
      ),
    );

    log("Classes API Response: ${response.data}");

    return switch (response.statusCode!) {
      >= 200 && < 300 =>
        ClassDataModel.fromJson(const JsonDecoder().convert(response.data)),
      404 => throw ServerException<String>("Classes data not found"),
      >= 400 && < 500 => throw ServerException<String>(response.data),
      _ => throw ServerException("${response.data}")
    };
  }
}
