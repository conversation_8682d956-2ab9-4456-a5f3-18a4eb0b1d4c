import '../../domain/entities/class_data.dart';

class ClassDataModel extends ClassData {
  const ClassDataModel({
    required super.status,
    required super.classes,
  });

  factory ClassDataModel.fromJson(Map<String, dynamic> json) {
    return ClassDataModel(
      status: json['status'] ?? '',
      classes: json['data'] != null
          ? (json['data'] as List)
              .map((x) => ClassItemModel.fromJson(x))
              .toList()
          : [],
    );
  }
}

class ClassItemModel extends ClassItem {
  const ClassItemModel({
    required super.classId,
    required super.classTitle,
    required super.classTime,
    required super.teacherName,
    required super.attendanceStatus,
  });

  factory ClassItemModel.fromJson(Map<String, dynamic> json) {
    return ClassItemModel(
      classId: json['ClassId'] ?? 0,
      classTitle: json['ClassTitle'] ?? '',
      classTime: json['ClassTime'] ?? '',
      teacherName: json['TeacherName'] ?? '',
      attendanceStatus: json['AttendanceStatus'] ?? '',
    );
  }
}
