import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/class_data.dart';
import '../../domain/repository/classes_repository.dart';
import '../datasource/classes_remote_datasource.dart';

class ClassesRepositoryImpl implements ClassesRepository {
  final ClassesRemoteDataSource remoteDataSource;

  ClassesRepositoryImpl(this.remoteDataSource);

  @override
  Future<Either<Failure, ClassData>> getClasses({
    required String classDay,
    required String language,
    required String date,
  }) async {
    try {
      final result = await remoteDataSource.getClasses(
        classDay: classDay,
        language: language,
        date: date,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
}
