import 'package:equatable/equatable.dart';

class ClassData extends Equatable {
  final String status;
  final List<ClassItem> classes;

  const ClassData({
    required this.status,
    required this.classes,
  });

  @override
  List<Object?> get props => [status, classes];
}

class ClassItem extends Equatable {
  final int classId;
  final String classTitle;
  final String classTime;
  final String teacherName;
  final String attendanceStatus;

  const ClassItem({
    required this.classId,
    required this.classTitle,
    required this.classTime,
    required this.teacherName,
    required this.attendanceStatus,
  });

  @override
  List<Object?> get props => [classId, classTitle, classTime, teacherName, attendanceStatus];
}
