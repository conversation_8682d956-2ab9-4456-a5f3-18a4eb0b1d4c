import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecase/base_usecase.dart';
import '../entities/class_data.dart';
import '../repository/classes_repository.dart';

class GetClassesUseCase implements UseCase<ClassData, ClassesParams> {
  final ClassesRepository repository;

  GetClassesUseCase(this.repository);

  @override
  Future<Either<Failure, ClassData>> call(ClassesParams params) async {
    return await repository.getClasses(
      classDay: params.classDay,
      language: params.language,
      date: params.date,
    );
  }
}

class ClassesParams extends Equatable {
  final String classDay;
  final String language;
  final String date;

  const ClassesParams({
    required this.classDay,
    required this.language,
    required this.date,
  });

  @override
  List<Object?> get props => [classDay, language, date];
}
