import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/app_font.dart';
import '../../../../core/utils/app_icon.dart';
import '../../../../core/utils/app_string.dart';
import '../../domain/entities/class_data.dart';

class CardClasses extends StatelessWidget {
  final Color? color;
  final ClassItem? classItem;

  const CardClasses({super.key, this.color, this.classItem});

  Color _getAttendanceColor(String status) {
    switch (status.toLowerCase()) {
      case 'attended':
        return AppColor.kclassesAttendedColor;
      case 'missed':
        return Colors.red;
      case 'apsent':
        return Colors.orange;
      default:
        return AppColor.kGrayColor;
    }
  }

  String _getAttendanceIcon(String status) {
    switch (status.toLowerCase()) {
      case 'attended':
        return AppIcon.kdone;
      case 'missed':
        return AppIcon.kdone; // You might want to use a different icon for missed
      case 'apsent':
        return AppIcon.kdone; // You might want to use a different icon for pending
      default:
        return AppIcon.kdone;
    }
  }

  @override
  Widget build(BuildContext context) {
    final title = classItem?.classTitle ?? "No Title";
    final teacher = classItem?.teacherName ?? "No Teacher";
    final status = classItem?.attendanceStatus ?? "Unknown";

    return Container(
      width: 258.h,
      height: 75.h,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  PoppinsText(
                    text: title,
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    textOverflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Row(
                    children: [
                      Container(
                        width: 24.w,
                        height: 24.h,
                        decoration: BoxDecoration(
                          color: AppColor.khomeListColor5,
                          borderRadius: BorderRadius.circular(100.r),
                        ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                    PoppinsText(
                          text: teacher,
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                          color: AppColor.khomeListColor6,
                          textOverflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 5.h),
              width: 50.w,
              height: 50.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5.r),
                color: _getAttendanceColor(status),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  SvgPicture.asset(_getAttendanceIcon(status)),
                  PoppinsText(
                    text: status,
                    fontSize: 8,
                    fontWeight: FontWeight.w500,
                    color: AppColor.kBgColor,
                    textOverflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
