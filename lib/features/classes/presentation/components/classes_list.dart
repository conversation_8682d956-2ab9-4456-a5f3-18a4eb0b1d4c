import 'dart:math';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/core/utils/enums.dart';
import 'package:edus_student_ms/features/classes/presentation/components/card_classes.dart';
import 'package:edus_student_ms/features/classes/presentation/components/center_text.dart';
import 'package:edus_student_ms/features/classes/presentation/components/dot.dart';
import 'package:edus_student_ms/features/classes/presentation/components/hour_classes.dart';
import 'package:edus_student_ms/features/classes/presentation/components/line.dart';
import 'package:edus_student_ms/features/classes/presentation/components/show_dialge.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../controller/classes_bloc.dart';

class ClassesList extends StatefulWidget {
  const ClassesList({super.key});

  @override
  State<ClassesList> createState() => _ClassesListState();
}

class _ClassesListState extends State<ClassesList> {
  final List<Color> colors = [
    AppColor.khomeListColor1.withOpacity(0.5),
    AppColor.khomeListColor2.withOpacity(0.5),
    AppColor.khomeListColor3.withOpacity(0.5),
    AppColor.khomeListColor4.withOpacity(0.5),
    AppColor.khomeListColor5.withOpacity(0.5),
  ];

  final Random random = Random();

  late List<Color> itemColors;

  @override
  void initState() {
    super.initState();
    // Generate a list of colors once and store it
    itemColors =
        List.generate(50, (index) => colors[random.nextInt(colors.length)]);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ClassesBloc, ClassesState>(
      builder: (context, state) {
        if (state.status == RequestStatus.loading) {
          return SizedBox(
            height: 532.h,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (state.status == RequestStatus.error) {
          return SizedBox(
            height: 532.h,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const PoppinsText(
                    text: "Error loading classes",
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.red,
                  ),
                  SizedBox(height: 16.h),
                  ElevatedButton(
                    onPressed: () {
                      // Retry logic can be added here
                    },
                    child: const PoppinsText(
                      text: "Retry",
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final classes = state.classData?.classes ?? [];

        return Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(children: [
                const CenterText(text: AppString.time),
                SizedBox(
                  width: 45.w,
                ),
                const CenterText(
                  text: AppString.kclass,
                ),
              ]),
            ),
            SizedBox(
              height: 8.h,
            ),
            SizedBox(
              height: 532.h,
              child: classes.isEmpty
                  ? const Center(
                      child: PoppinsText(
                        text: "No classes scheduled for this day",
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                      ),
                    )
                  : Stack(
                      children: [
                        const Line(),
                        ListView.builder(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          scrollDirection: Axis.vertical,
                          shrinkWrap: true,
                          itemCount: classes.length,
                          itemBuilder: (BuildContext context, int index) {
                            final classItem = classes[index];
                            return Row(
                              children: [
                                HourClasses(time: classItem.classTime),
                                SizedBox(
                                  width: 16.w,
                                ),
                                const Dot(),
                                SizedBox(
                                  width: 16.w,
                                ),
                                Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8.w),
                                  child: GestureDetector(
                                    onTap: () {
                                      showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return ShowDialoge(classItem: classItem);
                                        },
                                      );
                                    },
                                    child: CardClasses(
                                      color: itemColors[index % itemColors.length],
                                      classItem: classItem,
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
            ),
          ],
        );
      },
    );
  }
}
