import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/utils/app_font.dart';

class HourClasses extends StatelessWidget {
  final String? time;

  const HourClasses({super.key, this.time});

  String _formatTime(String? time) {
    if (time == null || time.isEmpty) return "00:00";
    try {
      // Parse time in format "HH:mm:ss" and return "HH:mm"
      final parts = time.split(':');
      if (parts.length >= 2) {
        return '${parts[0]}:${parts[1]}';
      }
      return time;
    } catch (e) {
      return time;
    }
  }

  @override
  Widget build(BuildContext context) {
    final formattedTime = _formatTime(time);

    return Column(
      children: [
        PoppinsText(
          text: formattedTime,
          fontSize: 13,
          fontWeight: FontWeight.w600,
        ),
        SizedBox(
          width: 4.w,
        ),
        Padding(
          padding: EdgeInsets.only(top: 4.h),
          child: PoppinsText(
            text: formattedTime,
            fontSize: 10,
            fontWeight: FontWeight.w600,
            color: AppColor.kGrayColor,
          ),
        ),
      ],
    );
  }
}
