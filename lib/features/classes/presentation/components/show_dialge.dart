import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

import '../../../../core/utils/app_icon.dart';
import '../../../../core/utils/app_string.dart';
import '../../domain/entities/class_data.dart';

class ShowDialoge extends StatelessWidget {
  final ClassItem? classItem;

  const ShowDialoge({super.key, this.classItem});

  Color _getAttendanceColor(String status) {
    switch (status.toLowerCase()) {
      case 'attended':
        return AppColor.kclassesAttendedColor;
      case 'missed':
        return Colors.red;
      case 'apsent':
        return Colors.orange;
      default:
        return AppColor.kGrayColor;
    }
  }

  String _formatTime(String? time) {
    if (time == null || time.isEmpty) return "00:00";
    try {
      final parts = time.split(':');
      if (parts.length >= 2) {
        return '${parts[0]}:${parts[1]}';
      }
      return time;
    } catch (e) {
      return time;
    }
  }

  @override
  Widget build(BuildContext context) {
    final title = classItem?.classTitle ?? "No Title";
    final teacher = classItem?.teacherName ?? "No Teacher";
    final status = classItem?.attendanceStatus ?? "Unknown";
    final time = classItem?.classTime ?? "00:00:00";
    final formattedTime = _formatTime(time);

    return AlertDialog(
      titlePadding: EdgeInsets.symmetric(horizontal: 18.h, vertical: 16.h),
      actionsPadding: EdgeInsets.symmetric(horizontal: 18.h, vertical: 20.h),
      contentPadding: EdgeInsets.symmetric(
          horizontal: 18.h, vertical: 8.h),
      backgroundColor: AppColor.kBgColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: PoppinsText(
              text: DateFormat('d EEEE').format(DateTime.now()),
              fontSize: 19,
              fontWeight: FontWeight.w700,
              textOverflow: TextOverflow.ellipsis,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            width: 50.0,
            height: 50.0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.0),
              color: _getAttendanceColor(status),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                SvgPicture.asset(AppIcon.kdone),
                PoppinsText(
                  text: status,
                  fontSize: 8,
                  fontWeight: FontWeight.w500,
                  color: AppColor.kBgColor,
                  textOverflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PoppinsText(
            text: title,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          const SizedBox(height: 10.0),
          Row(
            children: [
              Container(
                width: 24.0,
                height: 24.0,
                decoration: BoxDecoration(
                  color: AppColor.khomeListColor5,
                  borderRadius: BorderRadius.circular(100.0),
                ),
              ),
               SizedBox(width: 10.w),
              PoppinsText(
                  text: teacher,
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: AppColor.kBlackColor,
                  textOverflow: TextOverflow.ellipsis,
             
              ),
            ],
          ),
          const SizedBox(height: 10.0),
          Row(
            children: [
              SvgPicture.asset(AppIcon.khour),
              const SizedBox(width: 8.0),
              PoppinsText(
                text: formattedTime,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppColor.kBlackColor,
              ),
            ],
          ),
           SizedBox(height: 10.h),
          Row(
            children: [
              SvgPicture.asset(AppIcon.klocation),
              const SizedBox(width: 8.0),
              const PoppinsText(
                text: "City : ",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppColor.kBlackColor,
              ),
            ],
          ),
          const SizedBox(height: 10.0),
          Row(
            children: [
              SvgPicture.asset(AppIcon.kbook),
               SizedBox(width: 8.w),
              const Row(
                children: [
                  PoppinsText(
                    text: "Homework: ",
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColor.kBlackColor,
                  ),
                  // PoppinsText(
                  //   text: "Check assignment details",
                  //   fontSize: 14,
                  //   fontWeight: FontWeight.w400,
                  //   color: AppColor.kBlackColor,
                  // ),
                ],
              ),
            ],
          ),
        ],
      ),
      actions: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.0),
            color: AppColor.kPrimaryColor,
          ),
          child: Center(
            child: TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const PoppinsText(
                text: "Got it",
                color: AppColor.kBgColor,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
