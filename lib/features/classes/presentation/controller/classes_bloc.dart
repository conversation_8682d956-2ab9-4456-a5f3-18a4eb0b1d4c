import 'dart:async';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/utils/enums.dart';
import '../../domain/entities/class_data.dart';
import '../../domain/usecases/get_classes_usecase.dart';


// Events
abstract class ClassesEvent extends Equatable {
  const ClassesEvent();

  @override
  List<Object> get props => [];
}

class GetClassesEvent extends ClassesEvent {
  final String classDay;
  final String language;
  final String date;

  const GetClassesEvent({
    required this.classDay,
    required this.language,
    required this.date,
  });

  @override
  List<Object> get props => [classDay, language, date];
}

// State
class ClassesState extends Equatable {
  final RequestStatus status;
  final ClassData? classData;
  final String error;

  const ClassesState({
    this.status = RequestStatus.initial,
    this.classData,
    this.error = '',
  });

  ClassesState copyWith({
    RequestStatus? status,
    ClassData? classData,
    String? error,
  }) {
    return ClassesState(
      status: status ?? this.status,
      classData: classData ?? this.classData,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [status, classData, error];
}

class ClassesBloc extends Bloc<ClassesEvent, ClassesState> {
  final GetClassesUseCase _getClassesUseCase;

  ClassesBloc(this._getClassesUseCase) : super(const ClassesState()) {
    on<GetClassesEvent>(_getClassesEvent);
  }

  Future<void> _getClassesEvent(
      GetClassesEvent event, Emitter<ClassesState> emit) async {
    emit(state.copyWith(status: RequestStatus.loading));

    final result = await _getClassesUseCase(ClassesParams(
      classDay: event.classDay,
      language: event.language,
      date: event.date,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: RequestStatus.error,
        error: failure.toString(),
      )),
      (classData) => emit(state.copyWith(
        status: RequestStatus.success,
        classData: classData,
      )),
    );
  }
}
