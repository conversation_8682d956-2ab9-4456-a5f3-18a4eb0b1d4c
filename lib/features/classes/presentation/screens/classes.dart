import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/features/classes/presentation/components/calender.dart';
import 'package:edus_student_ms/features/classes/presentation/components/classes_list.dart';
import 'package:edus_student_ms/core/widgets/title_main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../controller/classes_bloc.dart';

class Classes extends StatefulWidget {
  const Classes({Key? key}) : super(key: key);

  @override
  State<Classes> createState() => _ClassesState();
}

class _ClassesState extends State<Classes> {
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    // Load classes for today initially
    _loadClassesForDate(_selectedDate);
  }

  void _loadClassesForDate(DateTime date) {
    final classDay = DateFormat('EEEE').format(date).toLowerCase();
    final dateString = DateFormat('yyyy-MM-dd HH:mm:ss').format(date);

    context.read<ClassesBloc>().add(GetClassesEvent(
      classDay: classDay,
      language: 'en',
      date: dateString,
    ));
  }

  void _onDateSelected(DateTime selectedDate) {
    setState(() {
      _selectedDate = selectedDate;
    });
    _loadClassesForDate(selectedDate);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverPadding(
              padding: EdgeInsets.symmetric(vertical: 17.h, horizontal: 0.w),
              sliver: SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TitleMain(title: AppString.classes, horizontal: 16.w),
                    SizedBox(height: 16.h),
                    CalendarWidget(
                      selectedDate: _selectedDate,
                      onDateSelected: _onDateSelected,
                    ),
                    SizedBox(height: 16.h),
                    const ClassesList(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
