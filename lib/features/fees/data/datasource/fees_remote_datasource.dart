import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/api_constant.dart';
import '../../../../core/services/service_locator.dart';
import '../../../../core/services/token_service/token_storage.dart';
import '../models/unpaid_fees_data_model.dart';

abstract class FeesRemoteDataSource {
  Future<UnpaidFeesDataModel> getUnpaidFees({
    required String language,
    required String month,
  });
}

final dio = Dio();

class FeesRemoteDataSourceImpl implements FeesRemoteDataSource {
  FeesRemoteDataSourceImpl();

  @override
  Future<UnpaidFeesDataModel> getUnpaidFees({
    required String language,
    required String month,
  }) async {
    final token = await sl<TokenStorage>().getToken();
    final response = await dio.get(
      ApiConstants.unpaidFeesUrl,
      queryParameters: {
        'language': language,
        'month': month,
      },
      options: Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
      ),
    );

    log("Unpaid Fees API Response: ${response.data}");

    return switch (response.statusCode!) {
      >= 200 && < 300 =>
        UnpaidFeesDataModel.fromJson(const JsonDecoder().convert(response.data)),
      404 => throw ServerException<String>("Unpaid fees data not found"),
      >= 400 && < 500 => throw ServerException<String>(response.data),
      _ => throw ServerException("${response.data}")
    };
  }
}
