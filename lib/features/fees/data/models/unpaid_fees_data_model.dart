import '../../domain/entities/unpaid_fees_data.dart';

class UnpaidFeesDataModel extends UnpaidFeesData {
  const UnpaidFeesDataModel({
    required super.status,
    required super.fees,
  });

  factory UnpaidFeesDataModel.fromJson(Map<String, dynamic> json) {
    return UnpaidFeesDataModel(
      status: json['status'] ?? '',
      fees: json['data'] != null
          ? (json['data'] as List)
              .map((x) => UnpaidFeeModel.fromJson(x))
              .toList()
          : [],
    );
  }
}

class UnpaidFeeModel extends UnpaidFee {
  const UnpaidFeeModel({
    required super.classId,
    required super.classTitle,
    required super.classPrice,
    required super.teacherName,
    required super.paymentStatus,
  });

  factory UnpaidFeeModel.fromJson(Map<String, dynamic> json) {
    return UnpaidFeeModel(
      classId: json['ClassId'] ?? 0,
      classTitle: json['ClassTitle'] ?? '',
      classPrice: json['ClassPrice'] ?? '0',
      teacherName: json['TeacherName'] ?? '',
      paymentStatus: json['PaymentStatus'] ?? '',
    );
  }
}
