import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/unpaid_fees_data.dart';
import '../../domain/repository/fees_repository.dart';
import '../datasource/fees_remote_datasource.dart';

class FeesRepositoryImpl implements FeesRepository {
  final FeesRemoteDataSource remoteDataSource;

  FeesRepositoryImpl(this.remoteDataSource);

  @override
  Future<Either<Failure, UnpaidFeesData>> getUnpaidFees({
    required String language,
    required String month,
  }) async {
    try {
      final result = await remoteDataSource.getUnpaidFees(
        language: language,
        month: month,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
}
