import 'package:equatable/equatable.dart';

class UnpaidFeesData extends Equatable {
  final String status;
  final List<UnpaidFee> fees;

  const UnpaidFeesData({
    required this.status,
    required this.fees,
  });

  @override
  List<Object?> get props => [status, fees];
}

class UnpaidFee extends Equatable {
  final int classId;
  final String classTitle;
  final String classPrice;
  final String teacherName;
  final String paymentStatus;

  const UnpaidFee({
    required this.classId,
    required this.classTitle,
    required this.classPrice,
    required this.teacherName,
    required this.paymentStatus,
  });

  @override
  List<Object?> get props => [classId, classTitle, classPrice, teacherName, paymentStatus];
}
