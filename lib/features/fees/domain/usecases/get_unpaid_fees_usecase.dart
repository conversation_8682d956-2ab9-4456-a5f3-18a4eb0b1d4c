import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecase/base_usecase.dart';
import '../entities/unpaid_fees_data.dart';
import '../repository/fees_repository.dart';

class GetUnpaidFeesUseCase implements UseCase<UnpaidFeesData, UnpaidFeesParams> {
  final FeesRepository repository;

  GetUnpaidFeesUseCase(this.repository);

  @override
  Future<Either<Failure, UnpaidFeesData>> call(UnpaidFeesParams params) async {
    return await repository.getUnpaidFees(
      language: params.language,
      month: params.month,
    );
  }
}

class UnpaidFeesParams extends Equatable {
  final String language;
  final String month;

  const UnpaidFeesParams({
    required this.language,
    required this.month,
  });

  @override
  List<Object?> get props => [language, month];
}
