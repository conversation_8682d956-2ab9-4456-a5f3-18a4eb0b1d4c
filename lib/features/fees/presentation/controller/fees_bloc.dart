import 'dart:async';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/utils/enums.dart';
import '../../domain/entities/unpaid_fees_data.dart';
import '../../domain/usecases/get_unpaid_fees_usecase.dart';

part 'fees_event.dart';
part 'fees_state.dart';

class FeesBloc extends Bloc<FeesEvent, FeesState> {
  final GetUnpaidFeesUseCase _getUnpaidFeesUseCase;

  FeesBloc(this._getUnpaidFeesUseCase) : super(const FeesState()) {
    on<GetUnpaidFeesEvent>(_getUnpaidFeesEvent);
  }

  Future<void> _getUnpaidFeesEvent(
      GetUnpaidFeesEvent event, Emitter<FeesState> emit) async {
    emit(state.copyWith(status: RequestStatus.loading));

    final result = await _getUnpaidFeesUseCase(UnpaidFeesParams(
      language: event.language,
      month: event.month,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: RequestStatus.error,
        error: failure.toString(),
      )),
      (feesData) => emit(state.copyWith(
        status: RequestStatus.success,
        feesData: feesData,
      )),
    );
  }
}

// Events
abstract class FeesEvent extends Equatable {
  const FeesEvent();

  @override
  List<Object> get props => [];
}

class GetUnpaidFeesEvent extends FeesEvent {
  final String language;
  final String month;

  const GetUnpaidFeesEvent({
    required this.language,
    required this.month,
  });

  @override
  List<Object> get props => [language, month];
}

// State
class FeesState extends Equatable {
  final RequestStatus status;
  final UnpaidFeesData? feesData;
  final String error;

  const FeesState({
    this.status = RequestStatus.initial,
    this.feesData,
    this.error = '',
  });

  FeesState copyWith({
    RequestStatus? status,
    UnpaidFeesData? feesData,
    String? error,
  }) {
    return FeesState(
      status: status ?? this.status,
      feesData: feesData ?? this.feesData,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [status, feesData, error];
}
