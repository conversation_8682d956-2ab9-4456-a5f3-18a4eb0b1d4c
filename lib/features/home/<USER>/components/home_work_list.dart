import 'dart:math';

import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../domain/entities/home_data.dart';
import '../controller/home_bloc.dart';

class HomeWorkList extends StatefulWidget {
  final bool showAll;
  const HomeWorkList({super.key, this.showAll = false});

  @override
  State<HomeWorkList> createState() => _HomeWorkListState();
}

class _HomeWorkListState extends State<HomeWorkList> {
  final List<Color> colors = [
    AppColor.khomeListColor1.withOpacity(0.5),
    AppColor.khomeListColor2.withOpacity(0.5),
    AppColor.khomeListColor3.withOpacity(0.5),
    AppColor.khomeListColor4.withOpacity(0.5),
    AppColor.khomeListColor5.withOpacity(0.5),
  ];

  final Random random = Random();

  late List<Color> itemColors;

  @override
  void initState() {
    super.initState();
    // Generate a list of colors once and store it
    itemColors =
        List.generate(50, (index) => colors[random.nextInt(colors.length)]);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        final allHomeworks = state.homeData?.homeworks ?? [];

        // Show only first 3 items if showAll is false
        final homeworks = widget.showAll
            ? allHomeworks
            : allHomeworks.take(3).toList();

        if (allHomeworks.isEmpty) {
          return SizedBox(
            height: 124.h,
            child: const Center(
              child: PoppinsText(
                text: "No homework assignments",
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          );
        }

        return SizedBox(
          height: 124.h,
          child: ListView.builder(
              padding: EdgeInsets.only(left: 16.w),
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              itemCount: homeworks.length,
              itemBuilder: (BuildContext context, int index) {
                final homework = homeworks[index];
                return Padding(
                  padding: EdgeInsets.only(right: 8.w),
                  child: Container(
                    width: 260.w,
                    height: 124.h,
                    decoration: BoxDecoration(
                        color: itemColors[index % itemColors.length],
                        borderRadius: BorderRadius.circular(10.r)),
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          PoppinsText(
                            text: homework.classTitle,
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                          ),
                          SizedBox(
                            height: 5.h,
                          ),
                          PoppinsText(
                              text: "Homework: ${homework.assignmentTitle}",
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              textOverflow: TextOverflow.ellipsis,
                              maxLines: 2,
                              textAlign: TextAlign.start,
                            ),
                        
                          SizedBox(
                            height: 5.h,
                          ),
                          Row(
                            children: [
                              Container(
                                width: 24.w,
                                height: 24.h,
                                decoration: BoxDecoration(
                                    color: Colors.greenAccent,
                                    borderRadius: BorderRadius.circular(100.r)),
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                               PoppinsText(
                                  text: homework.teacherName,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xff576B74),
                                  textOverflow: TextOverflow.ellipsis,
                                ),
                             
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                );
              }),
        );
      },
    );
  }
}
