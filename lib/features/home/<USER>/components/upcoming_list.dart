import 'dart:math';

import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../domain/entities/home_data.dart';
import '../controller/home_bloc.dart';

class UpcomingList extends StatefulWidget {
  final bool showAll;
  const UpcomingList({super.key, this.showAll = false});

  @override
  State<UpcomingList> createState() => _UpcomingListState();
}

class _UpcomingListState extends State<UpcomingList> {
  final List<Color> colors = [
     AppColor.khomeListColor1.withOpacity(0.5),
    AppColor.khomeListColor2.withOpacity(0.5),
    AppColor.khomeListColor3.withOpacity(0.5),
    AppColor.khomeListColor4.withOpacity(0.5),
    AppColor.khomeListColor5.withOpacity(0.5),
  ];

  final Random random = Random();

  late List<Color> itemColors;

  @override
  void initState() {
    super.initState();
    // Generate a list of colors once and store it
    itemColors =
        List.generate(50, (index) => colors[random.nextInt(colors.length)]);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        final allUpcomingClasses = state.homeData?.upcomingClasses ?? [];

        // Show only first 3 items if showAll is false
        final upcomingClasses = widget.showAll
            ? allUpcomingClasses
            : allUpcomingClasses.take(3).toList();

        if (allUpcomingClasses.isEmpty) {
          return SizedBox(
            height: 125.h,
            child: const Center(
              child: PoppinsText(
                text: "No upcoming classes",
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          );
        }

        return SizedBox(
          height: 125.h,
          child: ListView.builder(
              padding: EdgeInsets.only(left: 16.w),
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              itemCount: upcomingClasses.length,
              itemBuilder: (BuildContext context, int index) {
                final upcomingClass = upcomingClasses[index];
                return Padding(
                  padding: EdgeInsets.only(right: 8.w),
                  child: Container(
                    width: 174.w,
                    height: 124.h,
                    decoration: BoxDecoration(
                        color: itemColors[index % itemColors.length],
                        borderRadius: BorderRadius.circular(10.r)),
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          PoppinsText(
                            text: upcomingClass.classTitle,
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          Row(
                            children: [
                              PoppinsText(
                                text: _formatTime(upcomingClass.classTime),
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          Row(
                            children: [
                              Container(
                                width: 24.w,
                                height: 24.h,
                                decoration: BoxDecoration(
                                    color: Colors.greenAccent,
                                    borderRadius: BorderRadius.circular(100.r)
                                ),
                              ),
                              SizedBox(width: 4.w,),
                              Expanded(
                                child: PoppinsText(
                                  text: upcomingClass.teacherName,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                  color: const Color(0xff576B74),
                                  textOverflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                );
              }),
        );
      },
    );
  }

  String _formatTime(String time) {
    try {
      // Parse time in format "HH:mm:ss" and return "HH:mm"
      final parts = time.split(':');
      if (parts.length >= 2) {
        return '${parts[0]}:${parts[1]}';
      }
      return time;
    } catch (e) {
      return time;
    }
  }
}
