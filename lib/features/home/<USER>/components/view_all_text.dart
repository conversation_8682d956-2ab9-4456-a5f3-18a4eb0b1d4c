import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ViewAllText extends StatelessWidget {
  final String text1;
  final String text2;
  final bool showViewAll;
  final VoidCallback? onViewAllTap;

  const ViewAllText({
    super.key,
    required this.text1,
    required this.text2,
    this.showViewAll = true,
    this.onViewAllTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(children: [
        PoppinsText(
          text: text1,
          fontSize: 17,
          fontWeight: FontWeight.w700,
        ),
        const Spacer(),
        if (showViewAll)
          GestureDetector(
            onTap: onViewAllTap,
            child: PoppinsText(
              text: text2,
              fontSize: 14,
              color: const Color(0xff9CA2AA),
              fontWeight: FontWeight.w700,
            ),
          ),
      ]),
    );
  }
}
