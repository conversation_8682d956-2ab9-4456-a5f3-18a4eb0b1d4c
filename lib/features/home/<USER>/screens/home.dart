import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:edus_student_ms/features/home/<USER>/components/promo_bannel_Widget.dart';
import 'package:edus_student_ms/features/home/<USER>/components/view_all_text.dart';
import 'package:edus_student_ms/features/home/<USER>/components/welcom_row.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/utils/app_font.dart';
import '../../../../core/utils/enums.dart';
import '../components/home_work_list.dart';
import '../components/upcoming_list.dart';
import '../controller/home_bloc.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  bool _showAllUpcoming = false;
  bool _showAllHomework = false;

  @override
  void initState() {
    super.initState();
    // Trigger the API call when the screen loads
    context.read<HomeBloc>().add(const GetHomeDataEvent('en'));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: WelcomeRow(
            poppinsText: const PoppinsText(
                text: "Welcome, Waleed",
                fontSize: 24,
                fontWeight: FontWeight.w600),
            onTap: () {
              // Navigator.pushNamed(context, kNotifications);
            }),
      ),
      body: BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
          if (state.status == RequestStatus.loading) {
            return const Center(
              child: CircularProgressIndicator(color: AppColor.kPrimaryColor,),
            );
          }

          if (state.status == RequestStatus.error) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  PoppinsText(
                    text: "Error loading data",
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.red,
                  ),
                  SizedBox(height: 16.h),
                  ElevatedButton(
                    onPressed: () {
                      context.read<HomeBloc>().add(const GetHomeDataEvent('en'));
                    },
                    child: const PoppinsText(
                      text: "Retry",
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            );
          }

          return SafeArea(
            child: SingleChildScrollView(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 16.h,
                ),
                ViewAllText(
                  text1: AppString.upcomingClass,
                  text2: AppString.viewAll,
                  showViewAll: !_showAllUpcoming,
                  onViewAllTap: () {
                    setState(() {
                      _showAllUpcoming = true;
                    });
                  },
                ),
                SizedBox(
                  height: 12.h,
                ),
                UpcomingList(showAll: _showAllUpcoming),
                SizedBox(
                  height: 16.h,
                ),
                const EduspromotbannelWidget(),
                SizedBox(
                  height: 14.h,
                ),
                ViewAllText(
                  text1: AppString.homework,
                  text2: AppString.viewAll,
                  showViewAll: !_showAllHomework,
                  onViewAllTap: () {
                    setState(() {
                      _showAllHomework = true;
                    });
                  },
                ),
                SizedBox(
                  height: 12.h,
                ),
                HomeWorkList(showAll: _showAllHomework)
              ],
            )),
          );
        },
      ),
    );
  }
}
