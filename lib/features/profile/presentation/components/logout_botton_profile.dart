import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:edus_student_ms/core/utils/app_font.dart';
import 'package:edus_student_ms/core/utils/app_icon.dart';
import 'package:edus_student_ms/core/utils/app_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../../auth/presentation/controller/auth_bloc.dart';
import '../../../splash/splash_screen.dart';

class LogoutProfile extends StatelessWidget {
  const LogoutProfile({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _showLogoutDialog(context);
      },
      child: Container(
        height: 44.h,
        decoration: BoxDecoration(
            color: AppColor.klogout, borderRadius: BorderRadius.circular(10.r)),
        child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.all(4.h),
                child: SvgPicture.asset(AppIcon.klogoutProfile),
              ),
              SizedBox(
                width: 10.w,
              ),
              Padding(
                padding: EdgeInsets.symmetric(vertical: 1.5.h),
                child: const PoppinsText(
                  text: AppString.logOut,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppColor.kBgColor,
                ),
              )
            ]),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const PoppinsText(
            text: "Logout",
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
          content: const PoppinsText(
            text: "Are you sure you want to logout?",
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const PoppinsText(
                text: "Cancel",
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performLogout(context);
              },
              child: const PoppinsText(
                text: "Logout",
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.red,
              ),
            ),
          ],
        );
      },
    );
  }

  void _performLogout(BuildContext context) {
    // Trigger logout event
    context.read<AuthBloc>().add(const LogoutEvent());

    // Navigate to splash screen which will handle routing
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const SplashScreen()),
      (route) => false,
    );
  }
}
