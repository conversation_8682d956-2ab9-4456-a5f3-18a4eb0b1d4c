import 'package:edus_student_ms/core/utils/app_color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/services/service_locator.dart';
import '../../core/services/initialization_service.dart';
import '../../core/utils/app_font.dart';
import '../../core/utils/navigation.dart';
import '../auth/presentation/screens/auth/login.dart';
import '../onBoarding/onBoarding.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    // Add a small delay for splash screen effect
    await Future.delayed(const Duration(seconds: 1));

    if (!mounted) return;

    try {
      final initService = sl<InitializationService>();
      final initialRoute = await initService.determineInitialRoute();

      if (!mounted) return;

      Widget nextScreen;
      switch (initialRoute) {
        case AppInitialRoute.onboarding:
          nextScreen = const OnBoarding();
          break;
        case AppInitialRoute.login:
          nextScreen = const Login();
          break;
        case AppInitialRoute.home:
          nextScreen = const BottomNavigation();
          break;
      }

      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => nextScreen),
      );
    } catch (e) {
      // In case of error, default to onboarding
      if (!mounted) return;
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const OnBoarding()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:AppColor.kBgColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo or Icon
            Container(
              width: 120.w,
              height: 120.h,
              decoration: BoxDecoration(
                color: Colors.deepPurple.withOpacity(0.1),
                borderRadius: BorderRadius.circular(60.r),
              ),
              child: Icon(
                Icons.school,
                size: 60.sp,
                color: Colors.deepPurple,
              ),
            ),
            SizedBox(height: 24.h),
            PoppinsText(
              text: "Edus Student MS",
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: Colors.deepPurple,
            ),
            SizedBox(height: 8.h),
            PoppinsText(
              text: "Student Management System",
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Colors.grey,
            ),
            SizedBox(height: 40.h),
            // Loading indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.deepPurple),
            ),
          ],
        ),
      ),
    );
  }
}
