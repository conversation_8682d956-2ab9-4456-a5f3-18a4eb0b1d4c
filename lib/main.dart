import 'package:edus_student_ms/features/classes/presentation/controller/classes_bloc.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'core/config/app_routes.dart';
import 'core/services/service_locator.dart';
import 'features/auth/presentation/controller/auth_bloc.dart';
import 'features/home/<USER>/controller/home_bloc.dart';
import 'features/splash/splash_screen.dart';

void main() async {
  ServiceLocator().init();
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  runApp(
    MultiBlocProvider(providers: [
      BlocProvider<AuthBloc>(
        create: (context) => sl<AuthBloc>(),
      ),
      BlocProvider<HomeBloc>(
        create: (context) => sl<HomeBloc>(),
      ),
        BlocProvider<ClassesBloc>(
        create: (context) => sl<ClassesBloc>(),
      ),
    ], child: const MyApp()),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        minTextAdapt: true,
        designSize: const Size(375, 812),
        builder: (context, child) => MaterialApp(
              debugShowCheckedModeBanner: false,
              theme: ThemeData(
                primarySwatch: Colors.deepPurple,
              ),
              builder: (context, widget) {
                return MediaQuery(
                  data: MediaQuery.of(context).copyWith(
                      textScaler:
                          TextScaler.linear(1.0)), //old >> textScaleFactor: 1.0
                  child: widget!,
                );
              },
              home: const SplashScreen(),

              onGenerateRoute: AppRoutes.onGenerateRoutes,
            ));
  }
}
