import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:edus_student_ms/core/services/initialization_service.dart';
import 'package:edus_student_ms/core/services/token_service/token_storage.dart';

// Mock implementations for testing
class MockTokenStorage implements TokenStorage {
  String _token = '';
  int _userId = 0;

  @override
  Future<String> getToken() async => _token;

  @override
  void storeToken(String token) {
    _token = token;
  }

  @override
  Future<int> getUserId() async => _userId;

  @override
  void storeUserId(int userId) {
    _userId = userId;
  }

  @override
  Future<void> clearAll() async {
    _token = '';
    _userId = 0;
  }
}

class MockFlutterSecureStorage implements FlutterSecureStorage {
  final Map<String, String> _storage = {};
  final Map<String, List<ValueChanged<String?>>> _listeners = {};

  @override
  AndroidOptions get aOptions => AndroidOptions.defaultOptions;

  @override
  IOSOptions get iOptions => IOSOptions.defaultOptions;

  @override
  LinuxOptions get lOptions => LinuxOptions.defaultOptions;

  @override
  MacOsOptions get mOptions => MacOsOptions.defaultOptions;

  @override
  WebOptions get webOptions => WebOptions.defaultOptions;

  @override
  WindowsOptions get wOptions => WindowsOptions.defaultOptions;

  @override
  Future<String?> read({required String key, IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WebOptions? webOptions, MacOsOptions? mOptions, WindowsOptions? wOptions}) async {
    return _storage[key];
  }

  @override
  Future<void> write({required String key, required String? value, IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WebOptions? webOptions, MacOsOptions? mOptions, WindowsOptions? wOptions}) async {
    if (value != null) {
      _storage[key] = value;
    }
    // Notify listeners
    _notifyListeners(key, value);
  }

  @override
  Future<void> delete({required String key, IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WebOptions? webOptions, MacOsOptions? mOptions, WindowsOptions? wOptions}) async {
    _storage.remove(key);
    // Notify listeners with null value
    _notifyListeners(key, null);
  }

  @override
  Future<void> deleteAll({IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WebOptions? webOptions, MacOsOptions? mOptions, WindowsOptions? wOptions}) async {
    final keys = _storage.keys.toList();
    _storage.clear();
    // Notify all listeners with null values
    for (final key in keys) {
      _notifyListeners(key, null);
    }
  }

  @override
  Future<bool> containsKey({required String key, IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WebOptions? webOptions, MacOsOptions? mOptions, WindowsOptions? wOptions}) async {
    return _storage.containsKey(key);
  }

  @override
  Future<Map<String, String>> readAll({IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WebOptions? webOptions, MacOsOptions? mOptions, WindowsOptions? wOptions}) async {
    return Map.from(_storage);
  }

  @override
  Future<bool?> isCupertinoProtectedDataAvailable() async {
    // Mock implementation - return true for testing
    return true;
  }

  @override
  void registerListener({required String key, required ValueChanged<String?> listener}) {
    _listeners.putIfAbsent(key, () => []).add(listener);
  }

  @override
  void unregisterListener({required String key, required ValueChanged<String?> listener}) {
    _listeners[key]?.remove(listener);
    if (_listeners[key]?.isEmpty == true) {
      _listeners.remove(key);
    }
  }

  @override
  void unregisterAllListeners() {
    _listeners.clear();
  }

  @override
  void unregisterAllListenersForKey({required String key}) {
    _listeners.remove(key);
  }

  @override
  Stream<bool>? get onCupertinoProtectedDataAvailabilityChanged => null;

  // Helper method to notify listeners
  void _notifyListeners(String key, String? value) {
    final keyListeners = _listeners[key];
    if (keyListeners != null) {
      for (final listener in keyListeners) {
        listener(value);
      }
    }
  }
}

void main() {
  group('InitializationService', () {
    late InitializationService initService;
    late MockTokenStorage mockTokenStorage;
    late MockFlutterSecureStorage mockSecureStorage;

    setUp(() {
      mockTokenStorage = MockTokenStorage();
      mockSecureStorage = MockFlutterSecureStorage();
      initService = InitializationService(mockTokenStorage, mockSecureStorage);
    });

    test('should return onboarding for first launch', () async {
      // Act
      final result = await initService.determineInitialRoute();

      // Assert
      expect(result, AppInitialRoute.onboarding);
    });

    test('should return login when no token exists', () async {
      // Arrange - mark app as launched before
      await mockSecureStorage.write(key: 'app_launched_before', value: 'true');

      // Act
      final result = await initService.determineInitialRoute();

      // Assert
      expect(result, AppInitialRoute.login);
    });

    test('should return home when valid token exists', () async {
      // Arrange
      await mockSecureStorage.write(key: 'app_launched_before', value: 'true');
      mockTokenStorage.storeToken('valid_token');
      mockTokenStorage.storeUserId(123);

      // Act
      final result = await initService.determineInitialRoute();

      // Assert
      expect(result, AppInitialRoute.home);
    });

    test('should clear user data on logout', () async {
      // Arrange
      mockTokenStorage.storeToken('some_token');
      mockTokenStorage.storeUserId(123);

      // Act
      await initService.clearUserData();

      // Assert
      expect(await mockTokenStorage.getToken(), '');
      expect(await mockTokenStorage.getUserId(), 0);
    });
  });
}
